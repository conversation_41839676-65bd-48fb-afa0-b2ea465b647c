using System.ComponentModel.DataAnnotations.Schema;

namespace WingCore.domain.Models.StammDbModels;

[Table("Land_Kennung")]
public partial class LandKennung
{
    public string? Landesbezeichnung { get; set; }

    public string? Landeswährung { get; set; }

    public string? Umrechnungsfaktor { get; set; }

    public string? Auslandskennung { get; set; }

    public string? IntraKennung { get; set; }

    [Column("KFZKennz")]
    public string? Kfzkennz { get; set; }

    [Column("ID")]
    public string? Id { get; set; }
}
